/**
 * CliniCore Webhook Handler
 *
 * HTTP handler for CliniCore webhook events that processes patient synchronization
 * events from CliniCore to AutoPatient (AP). This module contains only the HTTP
 * request handler function, with all business logic extracted to dedicated processor
 * modules for better maintainability and testability.
 *
 * **Features:**
 * - Clean HTTP request/response handling
 * - Request ID extraction and correlation
 * - Webhook payload validation and parsing
 * - Error handling and response formatting
 * - Integration with CliniCore webhook processor modules
 * - Comprehensive logging with request tracing
 * - Performance monitoring and statistics
 *
 * **Supported Events:**
 * - EntityWasCreated (Patient) - Creates new patient in AP and local database
 * - EntityWasUpdated (Patient) - Updates existing patient in AP and local database
 * - Future: EntityWasDeleted, Appointment events, CustomField events
 *
 * **Processing Flow:**
 * 1. Extract and validate webhook payload
 * 2. Filter events (only Patient EntityWasCreated/EntityWasUpdated)
 * 3. Look up existing patient records
 * 4. Check sync buffer to prevent loops
 * 5. Map CC patient fields to AP contact format
 * 6. Upsert contact in AutoPatient
 * 7. Update local database with sync timestamps
 * 8. Return comprehensive processing results
 *
 * @fileoverview HTTP handler for CliniCore webhook events
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import { logError, logInfo, logWarn } from "@/utils/logger";
import { logError as logDbError, logWebhookError } from "@/utils/errorLogger";
import { processWebhookEvent } from "@/processors/ccWebhook";
import type { CCWebhookPayload } from "@/processors/ccWebhook";

/**
 * Handle CliniCore webhook events
 *
 * HTTP handler function that processes CliniCore webhook events for patient
 * synchronization. Extracts the request ID from the Hono context, validates
 * the webhook payload, delegates the processing logic to the processor module,
 * and returns a properly formatted JSON response.
 *
 * This handler is designed to be:
 * - **Lightweight**: Contains only HTTP-specific logic
 * - **Focused**: Single responsibility for request/response handling
 * - **Traceable**: Includes request ID correlation for debugging
 * - **Resilient**: Comprehensive error handling with proper HTTP status codes
 * - **Fast**: Optimized for webhook timeout requirements (25 seconds)
 *
 * **Event Filtering:**
 * - Processes only `EntityWasCreated` and `EntityWasUpdated` events
 * - Processes only `Patient` model events
 * - Designed to be extensible for future event types and models
 *
 * **Sync Buffer Logic:**
 * - Compares webhook `updatedAt` with database `ccUpdatedAt`
 * - Uses configurable sync buffer time (default: 60 seconds)
 * - Prevents sync loops and unnecessary processing
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with processing results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.post('/webhooks/cc', ccWebhookHandler);
 *
 * // Response format on success (200):
 * {
 *   "message": "CliniCore webhook processed successfully",
 *   "requestId": "req-123",
 *   "timestamp": "2024-07-27T10:30:00.000Z",
 *   "result": {
 *     "success": true,
 *     "event": "EntityWasUpdated",
 *     "model": "Patient",
 *     "entityId": 1766,
 *     "patientSync": {
 *       "action": "updated",
 *       "patient": {...},
 *       "apContact": {...},
 *       "stats": {...}
 *     },
 *     "metadata": {
 *       "durationMs": 1250,
 *       "requestId": "req-123"
 *     }
 *   }
 * }
 *
 * // Response format on skipped event (200):
 * {
 *   "message": "CliniCore webhook event skipped",
 *   "requestId": "req-123",
 *   "timestamp": "2024-07-27T10:30:00.000Z",
 *   "reason": "Event within sync buffer window (45.2s)",
 *   "result": {...}
 * }
 *
 * // Response format on error (400/500):
 * {
 *   "error": "CliniCore webhook processing failed",
 *   "details": "Invalid webhook payload structure",
 *   "requestId": "req-123",
 *   "timestamp": "2024-07-27T10:30:00.000Z",
 *   "stage": "validation"
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function ccWebhookHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");
	const timestamp = new Date().toISOString();

	try {
		logInfo(`[${requestId}] Received CliniCore webhook request`);

		// Parse and validate webhook payload
		const payload = await parseWebhookPayload(c, requestId);
		if (!payload) {
			return c.json(
				{
					error: "CliniCore webhook processing failed",
					details: "Invalid or missing webhook payload",
					requestId,
					timestamp,
					stage: "validation",
				},
				400,
			);
		}

		logInfo(
			`[${requestId}] Processing CC webhook: ${payload.event} ${payload.model} ID:${payload.id}`,
		);

		// Process the webhook event
		const result = await processWebhookEvent(payload, requestId);

		// Handle successful processing
		if (result.success) {
			const responseMessage = result.patientSync?.action === "skipped"
				? "CliniCore webhook event skipped"
				: "CliniCore webhook processed successfully";

			const response: Record<string, unknown> = {
				message: responseMessage,
				requestId,
				timestamp,
				result,
			};

			// Add skip reason if applicable
			if (result.patientSync?.action === "skipped") {
				response.reason = "Event processing was skipped (see result for details)";
			}

			logInfo(
				`[${requestId}] CC webhook processing completed: ${result.event} ${result.model} ` +
				`(${result.metadata.durationMs}ms)`,
			);

			return c.json(response, 200);
		}

		// Handle processing errors
		await logWebhookError(
			new Error(result.error?.message || "Unknown processing error"),
			"clinicore",
			payload,
		);

		logError(
			`[${requestId}] CC webhook processing failed: ${result.error?.message} ` +
			`(stage: ${result.error?.stage})`,
		);

		return c.json(
			{
				error: "CliniCore webhook processing failed",
				details: result.error?.message || "Unknown processing error",
				requestId,
				timestamp,
				stage: result.error?.stage || "unknown",
				entityId: result.entityId,
				event: result.event,
				model: result.model,
			},
			500,
		);

	} catch (error) {
		// Handle unexpected errors
		const errorMessage = error instanceof Error ? error.message : String(error);
		
		await logWebhookError(
			error instanceof Error ? error : new Error(errorMessage),
			"clinicore",
			{ error: "Unexpected handler error" },
		);

		logError(`[${requestId}] Unexpected error in CC webhook handler: ${errorMessage}`);

		return c.json(
			{
				error: "CliniCore webhook processing failed",
				details: "Unexpected server error",
				requestId,
				timestamp,
				stage: "handler",
			},
			500,
		);
	}
}

/**
 * Parse and validate webhook payload from request
 *
 * Extracts the JSON payload from the request body and validates its structure
 * to ensure it contains the required fields for webhook processing.
 *
 * @param c - Hono context object
 * @param requestId - Request ID for logging
 * @returns Parsed webhook payload or null if invalid
 */
async function parseWebhookPayload(
	c: Context,
	requestId: string,
): Promise<CCWebhookPayload | null> {
	try {
		const body = await c.req.json();

		// Validate required fields
		if (!body || typeof body !== "object") {
			logWarn(`[${requestId}] Webhook payload is not a valid object`);
			return null;
		}

		const { event, model, id, payload, timestamp } = body;

		// Check for required fields
		if (!event || !model || id === undefined || !payload) {
			logWarn(
				`[${requestId}] Webhook payload missing required fields: ` +
				`event=${!!event}, model=${!!model}, id=${id !== undefined}, payload=${!!payload}`,
			);
			return null;
		}

		// Validate field types
		if (typeof event !== "string" || typeof model !== "string" || typeof id !== "number") {
			logWarn(
				`[${requestId}] Webhook payload has invalid field types: ` +
				`event=${typeof event}, model=${typeof model}, id=${typeof id}`,
			);
			return null;
		}

		logInfo(
			`[${requestId}] Successfully parsed webhook payload: ${event} ${model} ID:${id}`,
		);

		return {
			event: event as CCWebhookPayload["event"],
			model: model as CCWebhookPayload["model"],
			id,
			payload,
			timestamp: timestamp || new Date().toISOString(),
		};

	} catch (error) {
		logError(`[${requestId}] Failed to parse webhook payload: ${error}`);
		return null;
	}
}
