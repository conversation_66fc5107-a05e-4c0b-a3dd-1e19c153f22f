/**
 * CliniCore Webhook Processing Module
 *
 * Comprehensive CliniCore webhook processing utilities for patient synchronization
 * and event handling. Provides complete webhook event processing, patient
 * synchronization, field mapping, and database operations with intelligent
 * algorithms and comprehensive error handling.
 *
 * **Core Modules:**
 * - `eventProcessor`: Main webhook event processing orchestrator
 * - `patientSynchronizer`: Patient synchronization operations and database management
 * - `fieldMapper`: CC to AP field mapping with standard field handling
 * - `types`: TypeScript interfaces and type definitions
 *
 * **Key Features:**
 * - Comprehensive webhook event filtering and validation
 * - Intelligent patient lookup with multiple strategies (ccId, email, phone)
 * - Sync buffer checking to prevent sync loops
 * - Complete field mapping from CliniCore to AutoPatient format
 * - Bidirectional database synchronization with error handling
 * - Detailed logging and statistics tracking
 * - Strict TypeScript compliance without `any` usage
 * - Performance-optimized for webhook timeout requirements
 *
 * **Supported Events:**
 * - EntityWasCreated (Patient)
 * - EntityWasUpdated (Patient)
 * - Future: EntityWasDeleted, Appointment events, CustomField events
 *
 * **Processing Flow:**
 * 1. Event filtering and validation
 * 2. Patient lookup in local database
 * 3. Sync buffer timing checks
 * 4. Field mapping from CC to AP format
 * 5. AutoPatient API upsert operations
 * 6. Database record updates with sync timestamps
 * 7. Comprehensive error handling and logging
 *
 * @example
 * ```typescript
 * import { processWebhookEvent } from '@processors/ccWebhook';
 *
 * // Process a CliniCore webhook event
 * const result = await processWebhookEvent(webhookPayload, "req-123");
 * 
 * if (result.success) {
 *   console.log(`Processed ${result.event} ${result.model} in ${result.metadata.durationMs}ms`);
 *   if (result.patientSync) {
 *     console.log(`Patient ${result.patientSync.action}: CC:${result.entityId} -> AP:${result.patientSync.apContact?.id}`);
 *   }
 * } else {
 *   console.error(`Processing failed at ${result.error?.stage}: ${result.error?.message}`);
 * }
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

// Main event processing functionality
export { processWebhookEvent } from "./eventProcessor";

// Patient synchronization utilities
export {
	lookupExistingPatient,
	checkSyncBuffer,
	upsertApContact,
	updatePatientRecord,
	createPatientRecord,
} from "./patientSynchronizer";

// Field mapping utilities
export { mapCcPatientToApContact } from "./fieldMapper";

// Type definitions
export type {
	CCWebhookPayload,
	CCPatientWebhookPayload,
	CCWebhookEvent,
	CCWebhookModel,
	EventProcessingContext,
	EventProcessingResult,
	PatientLookupResult,
	SyncBufferCheckResult,
	FieldMappingResult,
	PatientSyncResult,
	APContactUpsertResponse,
	PatientInsert,
	PatientSelect,
	WebhookProcessingConfig,
} from "./types";
