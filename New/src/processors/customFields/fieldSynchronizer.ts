/**
 * Custom Fields Synchronization Engine
 *
 * Provides comprehensive custom field synchronization between AutoPatient (AP)
 * and CliniCore (CC) platforms. Orchestrates field matching, conflict detection,
 * field creation, and database mapping operations to maintain bidirectional
 * field synchronization with intelligent conflict resolution.
 *
 * @fileoverview Main synchronization engine for custom field operations
 * @version 1.0.0
 * @since 2024-07-27
 */

import apiClient from "@apiClient";
import { dbSchema, getDb } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { eq } from "drizzle-orm";
import { logError as logDbError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { fieldsMatch, findExistingCustomField } from "./fieldMatcher";
import { createApFieldInCc, createCcFieldInAp } from "./fieldCreator";
import {
	checkForStandardFieldMapping,
	checkForExistingCustomFieldConflict,
	checkApFieldCreationBlocklist,
} from "./conflictDetector";
import {
	storeMappingForCreatedFields,
	storeStandardFieldMapping,
} from "./databaseOperations";
import type { CustomFieldSyncResponse, CustomFieldInsert } from "./types";

/**
 * Comprehensive custom field synchronization function
 *
 * Orchestrates the complete custom field synchronization process between
 * AutoPatient and CliniCore platforms. This function performs:
 *
 * 1. **Data Fetching**: Retrieves custom fields from both platforms
 * 2. **Field Matching**: Identifies matching field pairs using intelligent algorithms
 * 3. **Database Operations**: Stores/updates field mappings with upsert logic
 * 4. **Conflict Detection**: Identifies standard field conflicts and existing field conflicts
 * 5. **Field Creation**: Creates missing fields on target platforms with blocklist filtering
 * 6. **Statistics Tracking**: Provides comprehensive metrics and error reporting
 *
 * The synchronization process is designed to be:
 * - **Idempotent**: Safe to run multiple times without side effects
 * - **Comprehensive**: Handles all field types and edge cases
 * - **Resilient**: Continues processing despite individual field failures
 * - **Traceable**: Provides detailed logging with request ID correlation
 *
 * @param requestId - Request ID for tracing and logging correlation across all operations
 * @returns Promise resolving to comprehensive synchronization results with detailed statistics
 *
 * @throws {Error} When critical synchronization operations fail (logged but not propagated)
 *
 * @example
 * ```typescript
 * const syncResult = await synchronizeCustomFields("req-123");
 * 
 * console.log(`Synchronization completed:`);
 * console.log(`- Matched: ${syncResult.matchedCount} field pairs`);
 * console.log(`- Created: ${syncResult.creationStatistics.totalCreated} new fields`);
 * console.log(`- Blocked: ${syncResult.creationStatistics.creationBlockedCount} fields`);
 * console.log(`- Errors: ${syncResult.errors.length} general errors`);
 * 
 * if (syncResult.errors.length > 0) {
 *   console.error('Synchronization errors:', syncResult.errors);
 * }
 * 
 * // Process unmatched fields
 * for (const unmatchedField of syncResult.unmatchedApFields) {
 *   console.log(`Unmatched AP field: ${unmatchedField.name}`);
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function synchronizeCustomFields(
	requestId: string,
): Promise<CustomFieldSyncResponse> {
	logInfo("Starting custom field synchronization", { requestId });

	const response: CustomFieldSyncResponse = {
		matchedCount: 0,
		upsertedCount: 0,
		unmatchedApFields: [],
		unmatchedCcFields: [],
		createdCcFields: [],
		createdApFields: [],
		blockedApFields: [],
		standardFieldMappings: [],
		statistics: {
			totalApFields: 0,
			totalCcFields: 0,
			totalProcessed: 0,
			totalMatched: 0,
			totalUnmatched: 0,
			totalStandardMappings: 0,
		},
		creationStatistics: {
			apFieldsCreatedInCc: 0,
			ccFieldsCreatedInAp: 0,
			totalCreated: 0,
			creationErrors: 0,
			creationSkippedDueToStandardFields: 0,
			creationBlockedCount: 0,
		},
		errors: [],
		creationErrors: [],
	};

	try {
		// Phase 1: Data Fetching
		logDebug("Fetching custom fields from both platforms", { requestId });

		const [apCustomFields, ccCustomFields, existingMappings] =
			await Promise.all([
				apiClient.ap.apCustomfield.all(),
				apiClient.cc.ccCustomfieldReq.all(),
				getDb().select().from(dbSchema.customFields),
			]);

		logInfo("Data fetching completed", {
			requestId,
			apFieldsCount: apCustomFields.length,
			ccFieldsCount: ccCustomFields.length,
			existingMappingsCount: existingMappings.length,
		});

		// Update statistics
		response.statistics.totalApFields = apCustomFields.length;
		response.statistics.totalCcFields = ccCustomFields.length;
		response.statistics.totalProcessed =
			apCustomFields.length + ccCustomFields.length;

		// Phase 2: Field Matching and Database Operations
		const processedCcFields = new Set<number>();
		const db = getDb();

		for (const apField of apCustomFields) {
			let matchFound = false;

			logDebug("Processing AP field", {
				requestId,
				apFieldId: apField.id,
				apFieldName: apField.name,
				apFieldType: apField.dataType,
			});

			// Try to find a matching CC field
			for (const ccField of ccCustomFields) {
				if (processedCcFields.has(ccField.id)) {
					continue; // Skip already matched CC fields
				}

				if (fieldsMatch(apField, ccField)) {
					logDebug("Field match found", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						ccFieldLabel: ccField.label,
					});

					try {
						// Check if mapping already exists
						const existingMapping = existingMappings.find(
							(mapping) =>
								mapping.apId === apField.id || mapping.ccId === ccField.id,
						);

						const mappingData: CustomFieldInsert = {
							apId: apField.id,
							ccId: ccField.id,
							name: apField.name,
							label: ccField.label,
							type: apField.dataType,
							apConfig: apField,
							ccConfig: ccField,
							mappingType: "custom_to_custom",
							apStandardField: null,
							ccStandardField: null,
						};

						if (existingMapping) {
							// Update existing mapping
							await db
								.update(dbSchema.customFields)
								.set({
									...mappingData,
									updatedAt: new Date(),
								})
								.where(eq(dbSchema.customFields.id, existingMapping.id));

							logDebug("Updated existing field mapping", {
								requestId,
								mappingId: existingMapping.id,
								apFieldId: apField.id,
								ccFieldId: ccField.id,
							});
						} else {
							// Insert new mapping with upsert logic
							await db
								.insert(dbSchema.customFields)
								.values(mappingData)
								.onConflictDoUpdate({
									target: [dbSchema.customFields.apId],
									set: {
										ccId: mappingData.ccId,
										name: mappingData.name,
										label: mappingData.label,
										type: mappingData.type,
										apConfig: mappingData.apConfig,
										ccConfig: mappingData.ccConfig,
										mappingType: mappingData.mappingType,
										apStandardField: mappingData.apStandardField,
										ccStandardField: mappingData.ccStandardField,
										updatedAt: new Date(),
									},
								});

							logDebug("Created new field mapping", {
								requestId,
								apFieldId: apField.id,
								ccFieldId: ccField.id,
							});
						}

						response.upsertedCount++;
						processedCcFields.add(ccField.id);
						matchFound = true;
						break; // Move to next AP field
					} catch (error) {
						const errorMessage = `Failed to upsert field mapping: AP ${apField.id} -> CC ${ccField.id}`;
						logError(errorMessage, error);
						await logDbError(
							error instanceof Error ? error : new Error(String(error)),
							{
								type: "custom_field_sync",
								data: {
									apFieldId: apField.id,
									ccFieldId: ccField.id,
									operation: "upsert_mapping",
								},
							},
						);
						response.errors.push(errorMessage);
					}
				}
			}

			if (!matchFound) {
				response.unmatchedApFields.push(apField);
				logDebug("No match found for AP field", {
					requestId,
					apFieldId: apField.id,
					apFieldName: apField.name,
				});
			} else {
				response.matchedCount++;
			}
		}

		// Phase 3: Track unmatched CC fields
		for (const ccField of ccCustomFields) {
			if (!processedCcFields.has(ccField.id)) {
				response.unmatchedCcFields.push(ccField);
				logDebug("No match found for CC field", {
					requestId,
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					ccFieldLabel: ccField.label,
				});
			}
		}

		// Phase 4: Create unmatched AP fields in CC platform
		logInfo("Starting AP field creation in CC platform", {
			requestId,
			unmatchedApFieldsCount: response.unmatchedApFields.length,
		});

		for (const apField of response.unmatchedApFields) {
			try {
				// Check if this field maps to a CC standard field
				const standardMapping = checkForStandardFieldMapping(
					apField.name,
					"ap",
					"cc",
					requestId,
				);

				if (standardMapping) {
					// This field maps to a CC standard field, store the mapping instead of creating
					response.standardFieldMappings.push(standardMapping);
					response.creationStatistics.creationSkippedDueToStandardFields++;

					await storeStandardFieldMapping(apField, standardMapping, requestId);

					logInfo("Mapped AP custom field to CC standard field", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccStandardField: standardMapping.targetField,
					});
					continue;
				}

				// Check if this field is blocked from CC creation
				const blocklistEntry = checkApFieldCreationBlocklist(apField, requestId);
				if (blocklistEntry) {
					// Field is blocked, add to blocked list and skip creation
					response.blockedApFields.push(apField);
					response.creationStatistics.creationBlockedCount++;

					logInfo("Blocked AP field from CC creation", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						blocklistPattern: blocklistEntry.pattern,
						blocklistReason: blocklistEntry.reason,
						isRegex: blocklistEntry.isRegex,
					});
					continue;
				}

				// Check if this field conflicts with existing CC custom fields
				const existingCcField = checkForExistingCustomFieldConflict(
					apField,
					ccCustomFields,
					"cc",
					requestId,
				);

				if (existingCcField) {
					// Found existing CC custom field with same name, create mapping instead of duplicate
					response.creationStatistics.creationSkippedDueToStandardFields++; // Using same counter for simplicity

					await storeMappingForCreatedFields(apField, existingCcField as GetCCCustomField, requestId);

					logInfo("Mapped AP custom field to existing CC custom field", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						existingCcFieldId: existingCcField.id,
						existingCcFieldName: existingCcField.name,
					});
					continue;
				}

				// No conflicts, proceed with creation
				const createdCcField = await createApFieldInCc(apField, requestId);
				if (createdCcField) {
					response.createdCcFields.push(createdCcField);
					response.creationStatistics.apFieldsCreatedInCc++;

					// Store the mapping for the newly created field pair
					await storeMappingForCreatedFields(apField, createdCcField, requestId);

					logInfo("Successfully created and mapped CC field from AP field", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccFieldId: createdCcField.id,
						ccFieldName: createdCcField.name,
					});
				} else {
					// Creation failed, try to find existing field as fallback
					const existingCcField = findExistingCustomField(apField.name, ccCustomFields, "cc");
					if (existingCcField) {
						logInfo("Found existing CC field after creation failure, creating mapping", {
							requestId,
							apFieldId: apField.id,
							apFieldName: apField.name,
							existingCcFieldId: existingCcField.id,
							existingCcFieldName: existingCcField.name,
						});

						await storeMappingForCreatedFields(apField, existingCcField as GetCCCustomField, requestId);
						response.creationStatistics.creationSkippedDueToStandardFields++;
					}
				}
			} catch (error) {
				const errorMessage = `Failed to create CC field from AP field ${apField.id}: ${apField.name}`;
				logError(errorMessage, error);
				await logDbError(
					error instanceof Error ? error : new Error(String(error)),
					{
						type: "custom_field_creation",
						data: {
							apFieldId: apField.id,
							apFieldName: apField.name,
							operation: "create_cc_from_ap",
						},
					},
				);
				response.creationErrors.push(errorMessage);
				response.creationStatistics.creationErrors++;
			}
		}

		// Phase 5: Create unmatched CC fields in AP platform
		logInfo("Starting CC field creation in AP platform", {
			requestId,
			unmatchedCcFieldsCount: response.unmatchedCcFields.length,
		});

		for (const ccField of response.unmatchedCcFields) {
			try {
				// Check if this field maps to an AP standard field
				const standardMapping = checkForStandardFieldMapping(
					ccField.name,
					"cc",
					"ap",
					requestId,
				);

				if (standardMapping) {
					// This field maps to an AP standard field, store the mapping instead of creating
					response.standardFieldMappings.push(standardMapping);
					response.creationStatistics.creationSkippedDueToStandardFields++;

					await storeStandardFieldMapping(ccField, standardMapping, requestId);

					logInfo("Mapped CC custom field to AP standard field", {
						requestId,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						apStandardField: standardMapping.targetField,
					});
					continue;
				}

				// Check if this field conflicts with existing AP custom fields
				const existingApField = checkForExistingCustomFieldConflict(
					ccField,
					apCustomFields,
					"ap",
					requestId,
				);

				if (existingApField) {
					// Found existing AP custom field with same name, create mapping instead of duplicate
					response.creationStatistics.creationSkippedDueToStandardFields++; // Using same counter for simplicity

					await storeMappingForCreatedFields(existingApField as APGetCustomFieldType, ccField, requestId);

					logInfo("Mapped CC custom field to existing AP custom field", {
						requestId,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						existingApFieldId: existingApField.id,
						existingApFieldName: existingApField.name,
					});
					continue;
				}

				// No conflicts, proceed with creation
				const createdApField = await createCcFieldInAp(ccField, requestId);
				if (createdApField) {
					response.createdApFields.push(createdApField);
					response.creationStatistics.ccFieldsCreatedInAp++;

					// Store the mapping for the newly created field pair
					await storeMappingForCreatedFields(createdApField, ccField, requestId);

					logInfo("Successfully created and mapped AP field from CC field", {
						requestId,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						apFieldId: createdApField.id,
						apFieldName: createdApField.name,
					});
				} else {
					// Creation failed, try to find existing field as fallback
					const existingApField = findExistingCustomField(ccField.name, apCustomFields, "ap");
					if (existingApField) {
						logInfo("Found existing AP field after creation failure, creating mapping", {
							requestId,
							ccFieldId: ccField.id,
							ccFieldName: ccField.name,
							existingApFieldId: existingApField.id,
							existingApFieldName: existingApField.name,
						});

						await storeMappingForCreatedFields(existingApField as APGetCustomFieldType, ccField, requestId);
						response.creationStatistics.creationSkippedDueToStandardFields++;
					}
				}
			} catch (error) {
				const errorMessage = `Failed to create AP field from CC field ${ccField.id}: ${ccField.name}`;
				logError(errorMessage, error);
				await logDbError(
					error instanceof Error ? error : new Error(String(error)),
					{
						type: "custom_field_creation",
						data: {
							ccFieldId: ccField.id,
							ccFieldName: ccField.name,
							operation: "create_ap_from_cc",
						},
					},
				);
				response.creationErrors.push(errorMessage);
				response.creationStatistics.creationErrors++;
			}
		}

		// Update creation statistics
		response.creationStatistics.totalCreated =
			response.creationStatistics.apFieldsCreatedInCc +
			response.creationStatistics.ccFieldsCreatedInAp;

		// Update final statistics
		response.statistics.totalMatched = response.matchedCount;
		response.statistics.totalUnmatched =
			response.unmatchedApFields.length + response.unmatchedCcFields.length;
		response.statistics.totalStandardMappings = response.standardFieldMappings.length;

		logInfo("Custom field synchronization completed", {
			requestId,
			matchedCount: response.matchedCount,
			upsertedCount: response.upsertedCount,
			unmatchedApCount: response.unmatchedApFields.length,
			unmatchedCcCount: response.unmatchedCcFields.length,
			createdCcFieldsCount: response.createdCcFields.length,
			createdApFieldsCount: response.createdApFields.length,
			blockedApFieldsCount: response.blockedApFields.length,
			standardFieldMappingsCount: response.standardFieldMappings.length,
			totalCreatedCount: response.creationStatistics.totalCreated,
			conflictsResolvedCount: response.creationStatistics.creationSkippedDueToStandardFields,
			creationBlockedCount: response.creationStatistics.creationBlockedCount,
			errorsCount: response.errors.length,
			creationErrorsCount: response.creationErrors.length,
			summary: "Includes standard field mappings, existing custom field conflict resolution, and creation blocklist filtering",
		});

		return response;
	} catch (error) {
		const errorMessage = "Custom field synchronization failed";
		logError(errorMessage, error);
		await logDbError(
			error instanceof Error ? error : new Error(String(error)),
			{
				type: "custom_field_sync",
				data: { operation: "synchronize_custom_fields" },
			},
		);
		response.errors.push(errorMessage);
		return response;
	}
}
