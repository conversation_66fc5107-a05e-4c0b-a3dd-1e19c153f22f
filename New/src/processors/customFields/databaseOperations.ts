/**
 * Custom Fields Database Operations
 *
 * Provides database operations for custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Handles field mapping
 * storage, updates, and upsert operations with comprehensive error handling
 * and logging for data integrity and traceability.
 *
 * @fileoverview Database operations for custom field synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import { dbSchema, getDb } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { eq } from "drizzle-orm";
import { logDebug, logError } from "@/utils/logger";
import type { StandardFieldMapping } from "@/config/standardFieldMappings";
import type { CustomFieldInsert } from "./types";

/**
 * Store database mapping for newly created field pair
 *
 * Creates a bidirectional mapping record in the database for a newly created
 * field pair. This function handles both new insertions and updates to existing
 * mappings using upsert logic to prevent duplicate records.
 *
 * The mapping record includes:
 * - Field IDs from both platforms
 * - Field configuration data
 * - Mapping type classification
 * - Timestamps for tracking
 *
 * @param apField - AP field in the mapping relationship
 * @param ccField - CC field in the mapping relationship
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or data validation errors occur
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "patient_notes",
 *   dataType: "TEXTAREA"
 * };
 * 
 * const ccField: GetCCCustomField = {
 *   id: 456,
 *   name: "patient_notes",
 *   label: "Patient Notes",
 *   type: "TEXTAREA"
 * };
 * 
 * await storeMappingForCreatedFields(apField, ccField, "req-123");
 * console.log("Field mapping stored successfully");
 * ```
 *
 * @since 1.0.0
 */
export async function storeMappingForCreatedFields(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		const mappingData: CustomFieldInsert = {
			apId: apField.id,
			ccId: ccField.id,
			name: apField.name,
			label: ccField.label,
			type: ccField.type,
			apConfig: apField,
			ccConfig: ccField,
			mappingType: "custom_to_custom",
			apStandardField: null,
			ccStandardField: null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: [dbSchema.customFields.apId],
				set: {
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored mapping for newly created field pair", {
			requestId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
		});
	} catch (error) {
		logError(
			`Failed to store mapping for created fields: AP ${apField.id} -> CC ${ccField.id}`,
			error,
		);
		throw error;
	}
}

/**
 * Store database mapping for standard field mapping
 *
 * Creates a mapping record for a custom field that maps to a standard field
 * on the target platform. This prevents duplicate field creation and maintains
 * proper relationships between custom and standard fields.
 *
 * The function handles both AP-to-CC and CC-to-AP standard field mappings,
 * automatically determining the correct mapping type and field relationships
 * based on the source platform and mapping configuration.
 *
 * @param customField - Custom field (AP or CC) that maps to a standard field
 * @param standardFieldMapping - Standard field mapping information
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise that resolves when mapping is successfully stored
 *
 * @throws {Error} When database operation fails or data validation errors occur
 *
 * @example
 * ```typescript
 * const apCustomField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "email_address",
 *   dataType: "EMAIL"
 * };
 * 
 * const standardMapping: StandardFieldMapping = {
 *   sourceField: "email_address",
 *   targetField: "email",
 *   sourcePlatform: "ap",
 *   targetPlatform: "cc",
 *   notes: "Maps to CC standard email field"
 * };
 * 
 * await storeStandardFieldMapping(apCustomField, standardMapping, "req-123");
 * console.log("Standard field mapping stored successfully");
 * ```
 *
 * @since 1.0.0
 */
export async function storeStandardFieldMapping(
	customField: APGetCustomFieldType | GetCCCustomField,
	standardFieldMapping: StandardFieldMapping,
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		const isApCustomField = "dataType" in customField;

		const mappingData: CustomFieldInsert = {
			apId: isApCustomField ? customField.id : null,
			ccId: !isApCustomField ? customField.id : null,
			name: customField.name,
			label: isApCustomField ? customField.name : (customField as GetCCCustomField).label,
			type: isApCustomField ? customField.dataType : (customField as GetCCCustomField).type,
			apConfig: isApCustomField ? customField : null,
			ccConfig: !isApCustomField ? customField : null,
			mappingType: standardFieldMapping.sourcePlatform === "ap" ? "custom_to_standard" : "standard_to_custom",
			apStandardField: standardFieldMapping.targetPlatform === "ap" ? standardFieldMapping.targetField : null,
			ccStandardField: standardFieldMapping.targetPlatform === "cc" ? standardFieldMapping.targetField : null,
		};

		await db
			.insert(dbSchema.customFields)
			.values(mappingData)
			.onConflictDoUpdate({
				target: isApCustomField ? [dbSchema.customFields.apId] : [dbSchema.customFields.ccId],
				set: {
					apId: mappingData.apId,
					ccId: mappingData.ccId,
					name: mappingData.name,
					label: mappingData.label,
					type: mappingData.type,
					apConfig: mappingData.apConfig,
					ccConfig: mappingData.ccConfig,
					mappingType: mappingData.mappingType,
					apStandardField: mappingData.apStandardField,
					ccStandardField: mappingData.ccStandardField,
					updatedAt: new Date(),
				},
			});

		logDebug("Stored standard field mapping", {
			requestId,
			customFieldId: customField.id,
			customFieldName: customField.name,
			standardField: standardFieldMapping.targetField,
			mappingType: mappingData.mappingType,
		});
	} catch (error) {
		logError(
			`Failed to store standard field mapping for ${customField.name}`,
			error,
		);
		throw error;
	}
}

/**
 * Update existing field mapping with new data
 *
 * Updates an existing field mapping record with new field configurations
 * and metadata. This function is used when field properties change or
 * when mappings need to be refreshed with updated information.
 *
 * @param mappingId - ID of the existing mapping record to update
 * @param apField - Updated AP field configuration
 * @param ccField - Updated CC field configuration
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise that resolves when mapping is successfully updated
 *
 * @throws {Error} When database operation fails or mapping ID is invalid
 *
 * @example
 * ```typescript
 * await updateFieldMapping(
 *   existingMapping.id,
 *   updatedApField,
 *   updatedCcField,
 *   "req-123"
 * );
 * console.log("Field mapping updated successfully");
 * ```
 *
 * @since 1.0.0
 */
export async function updateFieldMapping(
	mappingId: string,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		
		await db
			.update(dbSchema.customFields)
			.set({
				name: apField.name,
				label: ccField.label,
				type: apField.dataType,
				apConfig: apField,
				ccConfig: ccField,
				updatedAt: new Date(),
			})
			.where(eq(dbSchema.customFields.id, mappingId));

		logDebug("Updated existing field mapping", {
			requestId,
			mappingId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
		});
	} catch (error) {
		logError(
			`Failed to update field mapping ${mappingId}`,
			error,
		);
		throw error;
	}
}

/**
 * Bulk upsert field mappings for improved performance
 *
 * Performs bulk upsert operations for multiple field mappings to improve
 * performance when processing large numbers of field relationships.
 * This function is optimized for batch operations during full synchronization.
 *
 * @param mappings - Array of field mapping data to upsert
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise that resolves when all mappings are successfully processed
 *
 * @throws {Error} When bulk operation fails or data validation errors occur
 *
 * @example
 * ```typescript
 * const mappings: CustomFieldInsert[] = [
 *   { apId: 1, ccId: 10, name: "field1", ... },
 *   { apId: 2, ccId: 20, name: "field2", ... },
 * ];
 * 
 * await bulkUpsertFieldMappings(mappings, "req-123");
 * console.log("Bulk field mappings completed successfully");
 * ```
 *
 * @since 1.0.0
 */
export async function bulkUpsertFieldMappings(
	mappings: CustomFieldInsert[],
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		
		// Process mappings in batches to avoid overwhelming the database
		const batchSize = 50;
		for (let i = 0; i < mappings.length; i += batchSize) {
			const batch = mappings.slice(i, i + batchSize);
			
			for (const mapping of batch) {
				await db
					.insert(dbSchema.customFields)
					.values(mapping)
					.onConflictDoUpdate({
						target: [dbSchema.customFields.apId],
						set: {
							ccId: mapping.ccId,
							name: mapping.name,
							label: mapping.label,
							type: mapping.type,
							apConfig: mapping.apConfig,
							ccConfig: mapping.ccConfig,
							mappingType: mapping.mappingType,
							apStandardField: mapping.apStandardField,
							ccStandardField: mapping.ccStandardField,
							updatedAt: new Date(),
						},
					});
			}
		}

		logDebug("Completed bulk upsert of field mappings", {
			requestId,
			totalMappings: mappings.length,
			batchSize,
		});
	} catch (error) {
		logError(
			`Failed to bulk upsert field mappings (${mappings.length} records)`,
			error,
		);
		throw error;
	}
}
