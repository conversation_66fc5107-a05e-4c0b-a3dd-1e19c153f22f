/**
 * Custom Fields Creation Operations
 *
 * Provides field creation functionality for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Handles bidirectional
 * field creation with proper error handling, logging, and conflict detection.
 *
 * @fileoverview Field creation utilities for custom field synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import apiClient from "@apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { apToCcCustomFieldConvert } from "./apToCcCustomFieldConvert";
import { ccToApCustomFieldConvert } from "./ccToApCustomFieldConvert";
import type { FieldCreationResult } from "./types";

/**
 * Create an AP field in CC platform using field conversion
 *
 * Converts an AutoPatient custom field to CliniCore format and creates it
 * on the CC platform. Handles conversion errors, API failures, and existing
 * field conflicts with comprehensive logging and error reporting.
 *
 * The function performs the following operations:
 * 1. Converts AP field to CC format using apToCcCustomFieldConvert
 * 2. Creates the field on CC platform via API
 * 3. Logs success/failure with detailed context
 * 4. Handles existing field conflicts gracefully
 *
 * @param apField - AP field to convert and create in CC
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to created CC field or null if creation failed
 *
 * @throws {Error} When field conversion fails or API request encounters unexpected errors
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "Patient Notes",
 *   dataType: "TEXTAREA",
 *   required: false
 * };
 * 
 * const createdField = await createApFieldInCc(apField, "req-123");
 * if (createdField) {
 *   console.log(`Created CC field: ${createdField.name} (ID: ${createdField.id})`);
 * } else {
 *   console.log("Field creation failed or field already exists");
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function createApFieldInCc(
	apField: APGetCustomFieldType,
	requestId: string,
): Promise<GetCCCustomField | null> {
	try {
		logDebug("Converting AP field to CC format", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			apFieldType: apField.dataType,
		});

		const ccFieldData = apToCcCustomFieldConvert(apField);
		const createdField = await apiClient.cc.ccCustomfieldReq.create(ccFieldData);

		logInfo("Successfully created CC field from AP field", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			ccFieldId: createdField.id,
			ccFieldName: createdField.name,
		});

		return createdField;
	} catch (error) {
		// Check if error indicates field already exists
		const errorMessage = error instanceof Error ? error.message : String(error);
		if (errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict")) {

			logInfo("CC field creation failed due to existing field", {
				requestId,
				apFieldId: apField.id,
				apFieldName: apField.name,
				errorMessage,
				action: "field_already_exists",
			});
		} else {
			logError(`Failed to create CC field from AP field ${apField.id}`, error);
		}
		return null;
	}
}

/**
 * Create a CC field in AP platform using field conversion
 *
 * Converts a CliniCore custom field to AutoPatient format and creates it
 * on the AP platform. Handles conversion errors, API failures, and existing
 * field conflicts with comprehensive logging and error reporting.
 *
 * The function performs the following operations:
 * 1. Converts CC field to AP format using ccToApCustomFieldConvert
 * 2. Creates the field on AP platform via API
 * 3. Logs success/failure with detailed context
 * 4. Handles existing field conflicts gracefully
 *
 * @param ccField - CC field to convert and create in AP
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to created AP field or null if creation failed
 *
 * @throws {Error} When field conversion fails or API request encounters unexpected errors
 *
 * @example
 * ```typescript
 * const ccField: GetCCCustomField = {
 *   id: 456,
 *   name: "medical_notes",
 *   label: "Medical Notes",
 *   type: "TEXTAREA",
 *   required: false
 * };
 * 
 * const createdField = await createCcFieldInAp(ccField, "req-456");
 * if (createdField) {
 *   console.log(`Created AP field: ${createdField.name} (ID: ${createdField.id})`);
 * } else {
 *   console.log("Field creation failed or field already exists");
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function createCcFieldInAp(
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | null> {
	try {
		logDebug("Converting CC field to AP format", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
		});

		const apFieldData = ccToApCustomFieldConvert(ccField);
		const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

		logInfo("Successfully created AP field from CC field", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			apFieldId: createdField.id,
			apFieldName: createdField.name,
		});

		return createdField;
	} catch (error) {
		// Check if error indicates field already exists
		const errorMessage = error instanceof Error ? error.message : String(error);
		if (errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken")) {

			logInfo("AP field creation failed due to existing field", {
				requestId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				errorMessage,
				action: "field_already_exists",
			});
		} else {
			logError(`Failed to create AP field from CC field ${ccField.id}`, error);
		}
		return null;
	}
}

/**
 * Create field with comprehensive result tracking
 *
 * Enhanced field creation function that provides detailed result information
 * including success status, created field data, and error details. This
 * function wraps the basic creation functions with additional result tracking.
 *
 * @param sourceField - Source field to create on target platform
 * @param targetPlatform - Platform where the field should be created ("ap" | "cc")
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to comprehensive creation result
 *
 * @example
 * ```typescript
 * const result = await createFieldWithResult(apField, "cc", "req-123");
 * 
 * if (result.success && result.field) {
 *   console.log(`Successfully created field: ${result.field.name}`);
 * } else {
 *   console.error(`Creation failed: ${result.error}`);
 *   if (result.existingFieldConflict) {
 *     console.log("Field already exists on target platform");
 *   }
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function createFieldWithResult(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatform: "ap" | "cc",
	requestId: string,
): Promise<FieldCreationResult> {
	try {
		let createdField: APGetCustomFieldType | GetCCCustomField | null = null;

		if (targetPlatform === "cc" && "dataType" in sourceField) {
			// Creating CC field from AP field
			createdField = await createApFieldInCc(sourceField as APGetCustomFieldType, requestId);
		} else if (targetPlatform === "ap" && "type" in sourceField) {
			// Creating AP field from CC field
			createdField = await createCcFieldInAp(sourceField as GetCCCustomField, requestId);
		} else {
			return {
				success: false,
				error: "Invalid source field type for target platform",
			};
		}

		if (createdField) {
			return {
				success: true,
				field: createdField,
			};
		} else {
			return {
				success: false,
				error: "Field creation failed",
				existingFieldConflict: true,
			};
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const isExistingFieldConflict = errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken");

		return {
			success: false,
			error: errorMessage,
			existingFieldConflict: isExistingFieldConflict,
		};
	}
}
